import React, { useEffect, useState } from 'react';
import * as SunCalc from 'suncalc';

interface DebugPanelProps {
  // Pas de props pour le moment
}

const DebugPanel: React.FC<DebugPanelProps> = () => {
  const [debugInfo, setDebugInfo] = useState<{
    currentTime: string;
    currentHour: number;
    sunrise: string;
    sunset: string;
    nauticalDawn: string;
    nauticalDusk: string;
    starsOpacity: number;
    phase: string;
  } | null>(null);

  // Position par défaut (Paris)
  const userLocation = { lat: 48.8566, lon: 2.3522 };

  // Calculer l'opacité des étoiles (copie de la logique d'AstronomicalLayer)
  const calculateStarsOpacity = (currentTime: Date): number => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Période de jour complet
    if (currentHour >= sunrise && currentHour <= sunset) {
      return 0;
    }

    // CRÉPUSCULE DU SOIR
    if (currentHour > sunset && currentHour <= nauticalDusk) {
      const progress = (currentHour - sunset) / (nauticalDusk - sunset);
      return progress * 0.6;
    }

    // DÉBUT DE NUIT
    if (currentHour > nauticalDusk && currentHour <= nauticalDusk + 0.75) {
      const progress = (currentHour - nauticalDusk) / 0.75;
      return 0.6 + (progress * 0.4);
    }

    // NUIT COMPLÈTE
    if (currentHour > nauticalDusk + 0.75 && currentHour < nauticalDawn - 0.75) {
      return 1.0;
    }

    // FIN DE NUIT
    if (currentHour >= nauticalDawn - 0.75 && currentHour < nauticalDawn) {
      const progress = (currentHour - (nauticalDawn - 0.75)) / 0.75;
      return 1.0 - (progress * 0.4);
    }

    // CRÉPUSCULE DU MATIN
    if (currentHour >= nauticalDawn && currentHour < sunrise) {
      const progress = (currentHour - nauticalDawn) / (sunrise - nauticalDawn);
      return 0.6 * (1 - progress);
    }

    return 0;
  };

  // Déterminer la phase actuelle
  const getCurrentPhase = (currentTime: Date): string => {
    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    if (currentHour >= sunrise && currentHour <= sunset) return "☀️ JOUR";
    if (currentHour > sunset && currentHour <= nauticalDusk) return "🌅 CRÉPUSCULE SOIR";
    if (currentHour > nauticalDusk && currentHour <= nauticalDusk + 0.75) return "🌌 DÉBUT NUIT";
    if (currentHour > nauticalDusk + 0.75 && currentHour < nauticalDawn - 0.75) return "🌙 NUIT COMPLÈTE";
    if (currentHour >= nauticalDawn - 0.75 && currentHour < nauticalDawn) return "🌄 FIN NUIT";
    if (currentHour >= nauticalDawn && currentHour < sunrise) return "🌇 CRÉPUSCULE MATIN";
    
    return "❓ INDÉTERMINÉ";
  };

  // Mettre à jour les informations de debug
  const updateDebugInfo = () => {
    const now = new Date();
    const sunTimes = SunCalc.getTimes(now, userLocation.lat, userLocation.lon);
    
    const currentHour = now.getHours() + now.getMinutes() / 60 + now.getSeconds() / 3600;
    const starsOpacity = calculateStarsOpacity(now);
    const phase = getCurrentPhase(now);

    setDebugInfo({
      currentTime: now.toLocaleTimeString(),
      currentHour: currentHour,
      sunrise: sunTimes.sunrise.toLocaleTimeString(),
      sunset: sunTimes.sunset.toLocaleTimeString(),
      nauticalDawn: sunTimes.nauticalDawn.toLocaleTimeString(),
      nauticalDusk: sunTimes.nauticalDusk.toLocaleTimeString(),
      starsOpacity: starsOpacity,
      phase: phase
    });
  };

  useEffect(() => {
    // Mise à jour initiale
    updateDebugInfo();
    
    // Mise à jour toutes les secondes
    const interval = setInterval(updateDebugInfo, 1000);
    
    return () => clearInterval(interval);
  }, []);

  if (!debugInfo) return null;

  return (
    <div className="fixed top-4 left-4 bg-black/80 text-white p-4 rounded-lg text-sm font-mono z-50 backdrop-blur-sm">
      <h3 className="text-yellow-400 font-bold mb-2">🔍 DEBUG ASTRONOMIQUE</h3>
      <div className="space-y-1">
        <div>⏰ Heure: <span className="text-cyan-400">{debugInfo.currentTime}</span> ({debugInfo.currentHour.toFixed(2)}h)</div>
        <div>🌅 Lever: <span className="text-orange-400">{debugInfo.sunrise}</span></div>
        <div>🌇 Coucher: <span className="text-orange-400">{debugInfo.sunset}</span></div>
        <div>🌄 Aube nautique: <span className="text-blue-400">{debugInfo.nauticalDawn}</span></div>
        <div>🌌 Crépuscule nautique: <span className="text-purple-400">{debugInfo.nauticalDusk}</span></div>
        <div>⭐ Étoiles: <span className="text-yellow-300">{(debugInfo.starsOpacity * 100).toFixed(0)}%</span></div>
        <div>📍 Phase: <span className="text-green-400">{debugInfo.phase}</span></div>
      </div>
    </div>
  );
};

export default DebugPanel;
